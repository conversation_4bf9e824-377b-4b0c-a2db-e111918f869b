const BatchController = require('../controller/batch_controller');

describe('BatchController Security Tests', () => {
  let batchController;
  let mockReq;
  let mockRes;

  beforeEach(() => {
    batchController = new BatchController();
    
    mockRes = {
      json: jest.fn(),
      status: jest.fn().mockReturnThis()
    };
  });

  describe('getBatchByBatchNo - Authorization Tests', () => {
    test('should allow access to own batch', async () => {
      mockReq = {
        params: { batchNo: '********-INTER-SIN01-MERCHANT123' },
        merchant: { merchant_account_number: 'MERCHANT123' }
      };

      await batchController.getBatchByBatchNo(mockReq, mockRes);

      expect(mockRes.status).not.toHaveBeenCalledWith(403);
      expect(mockRes.json).toHaveBeenCalledWith({
        isSuccess: true,
        data: expect.objectContaining({
          new_batch_no: expect.any(String),
          date: '********'
        })
      });
    });

    test('should deny access to another merchant\'s batch', async () => {
      mockReq = {
        params: { batchNo: '********-INTER-SIN01-MERCHANT123' },
        merchant: { merchant_account_number: 'MERCHANT456' }
      };

      await batchController.getBatchByBatchNo(mockReq, mockRes);

      expect(mockRes.status).toHaveBeenCalledWith(403);
      expect(mockRes.json).toHaveBeenCalledWith({
        isSuccess: false,
        message: 'You do not have permission to access this batch'
      });
    });

    test('should deny access when merchant account number is missing', async () => {
      mockReq = {
        params: { batchNo: '********-INTER-SIN01-MERCHANT123' },
        merchant: {}
      };

      await batchController.getBatchByBatchNo(mockReq, mockRes);

      expect(mockRes.status).toHaveBeenCalledWith(403);
      expect(mockRes.json).toHaveBeenCalledWith({
        isSuccess: false,
        message: 'You do not have permission to access this batch'
      });
    });

    test('should deny access for malformed batch number', async () => {
      mockReq = {
        params: { batchNo: 'invalid-batch-format' },
        merchant: { merchant_account_number: 'MERCHANT123' }
      };

      await batchController.getBatchByBatchNo(mockReq, mockRes);

      expect(mockRes.status).toHaveBeenCalledWith(403);
      expect(mockRes.json).toHaveBeenCalledWith({
        isSuccess: false,
        message: 'You do not have permission to access this batch'
      });
    });

    test('should handle empty batch number', async () => {
      mockReq = {
        params: { batchNo: '' },
        merchant: { merchant_account_number: 'MERCHANT123' }
      };

      await batchController.getBatchByBatchNo(mockReq, mockRes);

      expect(mockRes.status).toHaveBeenCalledWith(403);
      expect(mockRes.json).toHaveBeenCalledWith({
        isSuccess: false,
        message: 'You do not have permission to access this batch'
      });
    });
  });

  describe('validateBatchOwnership - Unit Tests', () => {
    test('should return true for matching merchant account', () => {
      const result = batchController.validateBatchOwnership(
        '********-INTER-SIN01-MERCHANT123',
        'MERCHANT123'
      );
      expect(result).toBe(true);
    });

    test('should return false for non-matching merchant account', () => {
      const result = batchController.validateBatchOwnership(
        '********-INTER-SIN01-MERCHANT123',
        'MERCHANT456'
      );
      expect(result).toBe(false);
    });

    test('should return false for malformed batch number', () => {
      const result = batchController.validateBatchOwnership(
        'invalid-batch',
        'MERCHANT123'
      );
      expect(result).toBe(false);
    });

    test('should return false for empty batch number', () => {
      const result = batchController.validateBatchOwnership(
        '',
        'MERCHANT123'
      );
      expect(result).toBe(false);
    });

    test('should return false for null batch number', () => {
      const result = batchController.validateBatchOwnership(
        null,
        'MERCHANT123'
      );
      expect(result).toBe(false);
    });

    test('should return false for empty merchant account', () => {
      const result = batchController.validateBatchOwnership(
        '********-INTER-SIN01-MERCHANT123',
        ''
      );
      expect(result).toBe(false);
    });

    test('should return false for null merchant account', () => {
      const result = batchController.validateBatchOwnership(
        '********-INTER-SIN01-MERCHANT123',
        null
      );
      expect(result).toBe(false);
    });

    test('should handle batch number with extra dashes', () => {
      const result = batchController.validateBatchOwnership(
        '********-INTER-SIN01-MERCHANT123-EXTRA',
        'MERCHANT123'
      );
      expect(result).toBe(true);
    });

    test('should handle different batch formats', () => {
      const testCases = [
        {
          batchNo: '********-B2B-SIN01-MERCHANT123',
          merchantAccount: 'MERCHANT123',
          expected: true
        },
        {
          batchNo: '********-EXPRE-HKG01-MERCHANT456',
          merchantAccount: 'MERCHANT456',
          expected: true
        },
        {
          batchNo: '********-EXPRE-HKG01-MERCHANT456',
          merchantAccount: 'MERCHANT123',
          expected: false
        }
      ];

      testCases.forEach(({ batchNo, merchantAccount, expected }) => {
        const result = batchController.validateBatchOwnership(batchNo, merchantAccount);
        expect(result).toBe(expected);
      });
    });
  });
});
